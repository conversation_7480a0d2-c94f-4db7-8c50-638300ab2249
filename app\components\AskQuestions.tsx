"use client";
import React, { useState, useRef, useEffect } from "react";

const DUMMY_RESPONSE =
  "This is a demo answer. Please consult a professional for medical advice.";
const MORE_QUESTIONS_URL =
  "https://app.meetaugust.ai/join/wa?message=Hello%20August&utm=ask_questions_widget";

const AskQuestions = () => {
  const chatEmbedRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Function to initialize the chat widget
    const initializeChatWidget = () => {
      if (typeof window !== "undefined") {
        // First try the global reinitialize function we set up in layout
        if ((window as any).reinitializeChatWidget) {
          (window as any).reinitializeChatWidget();
          return true;
        }

        // Fallback: Try direct initialization functions
        if ((window as any).initializeHealthLibraryChat) {
          (window as any).initializeHealthLibraryChat();
          return true;
        }

        // If we have the chat embed element, try other common patterns
        if (chatEmbedRef.current) {
          const chatEmbed = chatEmbedRef.current;

          // Dispatch a custom event that the external script might be listening for
          const event = new CustomEvent("chatEmbedReady", {
            detail: { element: chatEmbed },
          });
          window.dispatchEvent(event);

          // Try other common initialization patterns
          if ((window as any).initChat) {
            (window as any).initChat();
            return true;
          } else if ((window as any).loadChatWidget) {
            (window as any).loadChatWidget();
            return true;
          } else if (
            (window as any).augustChat &&
            (window as any).augustChat.init
          ) {
            (window as any).augustChat.init();
            return true;
          }
        }
      }
      return false;
    };

    // Try to initialize immediately
    initializeChatWidget();

    // Also try after a short delay to ensure the script has fully loaded
    const timeoutId = setTimeout(initializeChatWidget, 100);

    // Try again after a longer delay for slower connections
    const longerTimeoutId = setTimeout(initializeChatWidget, 500);

    // Set up a MutationObserver to watch for changes in the chat embed element
    // This helps detect if the external script modifies the DOM
    let observer: MutationObserver | null = null;
    if (chatEmbedRef.current) {
      observer = new MutationObserver(() => {
        // If the chat embed is empty or has been reset, try to reinitialize
        const chatEmbed = chatEmbedRef.current;
        if (chatEmbed && chatEmbed.children.length === 0) {
          setTimeout(initializeChatWidget, 50);
        }
      });

      observer.observe(chatEmbedRef.current, {
        childList: true,
        subtree: true,
      });
    }

    // Cleanup
    return () => {
      clearTimeout(timeoutId);
      clearTimeout(longerTimeoutId);
      if (observer) {
        observer.disconnect();
      }
    };
  }, []);

  return (
    <div id="ask-questions-widget" className="max-w-3xl mx-auto p-6 bg-white">
      {/* Header Section */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center">
            <div className="text-white text-lg font-bold">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="size-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M8.625 12a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 0 1-2.555-.337A5.972 5.972 0 0 1 5.41 20.97a5.969 5.969 0 0 1-.474-.065 4.48 4.48 0 0 0 .978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25Z"
                />
              </svg>
            </div>
          </div>
          <h1 className="text-3xl font-semibold text-gray-800">
            Have Questions?
          </h1>
        </div>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Have a question on this topic? Submit it here and get an instant
          answer from our{" "}
          <a href={MORE_QUESTIONS_URL} className="text-teal-600 font-medium">
            AI Doctor
          </a>
          .
        </p>
      </div>
      <div
        ref={chatEmbedRef}
        className="chat-embed mx-auto border border-grey-600 pl-[32px] rounded-lg"
        style={{ height: "100%", width: "100%" }}
      />

      {/* Disclaimer */}
    </div>
  );
};

export default AskQuestions;
